'use client';

import React, { useMemo, useCallback, useState } from 'react';

import { Save, Truck, X, AlertCircle, CheckCircle2 } from 'lucide-react';

import { Button } from '@/shared/components/button';
import { Checkbox } from '@/shared/components/checkbox';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { cn } from '@/core/lib/utils';
import type { Task, Vehicle } from '@/core/types';

interface TankTruckDispatchModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: Partial<Task>;
  vehicle?: Partial<Vehicle>;
  onConfirm: (dispatchData: TankTruckDispatchData) => void;
}

export interface TankTruckDispatchData {
  // 基础信息
  dispatchNumber: string;
  taskNumber: string;
  contractNumber: string;

  // 第二行字段
  materialType: string;
  mortarType: string;
  projectName: string;

  // 第三行字段
  stationNumber: string;
  mainOperator: string;
  constructionUnit: string;

  // 第四行字段
  vehicleNumber: string;
  driverName: string;
  constructionPart: string;

  // 第五行字段
  qualityInspector: string;
  outputTemperature: string;
  strengthGrade: string;

  // 第六行字段
  pouringMethod: string;
  siteDispatcher: string;
  dispatcher: string;

  // 第七行字段 - 方量设定
  maxSetting: boolean;
  noOverload: boolean;
  adjustByWeight: boolean;
  printVolume: number;
  printWeight: number;
  dispatchMethod: string;

  // 右侧设置
  productionTip: string;
  driverTip: string;
  pumpTruckNumber: string;
  printTip: string;

  // 复选框选项
  autoDispatch: boolean;
  backSandMortar: boolean;
  washingWater: boolean;
  withPump: boolean;
  noMixingStation: boolean;
  allowHouseRepair: boolean;

  // 底部
  printTicket: boolean;
  ticketNote: string;
}

export const TankTruckDispatchModal: React.FC<TankTruckDispatchModalProps> = ({
  open,
  onOpenChange,
  task,
  vehicle,
  onConfirm,
}) => {
  console.log('🚛 TankTruckDispatchModal render:', {
    open,
    task: task?.taskNumber,
    vehicle: vehicle?.vehicleNumber,
    hasOnConfirm: !!onConfirm,
  });
  const [formData, setFormData] = useState<TankTruckDispatchData>({
    // 基础信息
    dispatchNumber: '新发货单',
    taskNumber: task?.taskNumber || 'C125-00003',
    contractNumber: task?.contractNumber || 'C125-00002',

    // 第二行字段
    materialType: '砼',
    mortarType: '',
    projectName: task?.projectName || '个人自建',

    // 第三行字段
    stationNumber: '站点1',
    mainOperator: '',
    constructionUnit: task?.constructionUnit || '长治市郊善堂洗煤工程有限公司',

    // 第四行字段
    vehicleNumber: vehicle?.vehicleNumber || '10',
    driverName: vehicle?.driverName || '未志国',
    constructionPart: '垫层',

    // 第五行字段
    qualityInspector: '',
    outputTemperature: '',
    strengthGrade: task?.strength || 'C15',

    // 第六行字段
    pouringMethod: '自卸',
    siteDispatcher: '',
    dispatcher: '系统管理员',

    // 第七行字段 - 方量设定
    maxSetting: true,
    noOverload: false,
    adjustByWeight: false,
    printVolume: 6.57,
    printWeight: 130.04,
    dispatchMethod: '生成发货单',

    // 右侧设置
    productionTip: '',
    driverTip: '',
    pumpTruckNumber: '',
    printTip: '',

    // 复选框选项
    autoDispatch: false,
    backSandMortar: false,
    washingWater: false,
    withPump: false,
    noMixingStation: false,
    allowHouseRepair: false,

    // 底部
    printTicket: false,
    ticketNote: '',
  });

  // 货物类型和砂浆类型的联动选项
  const getMortarTypeOptions = (materialType: string) => {
    switch (materialType) {
      case '砼':
        return []; // 砼类型时砂浆类型不可选择
      case '砂浆':
        return ['接茬', '润管', '抹灰', '砌筑', '防水'];
      case '水票':
        return ['内部结算', '客户结算'];
      default:
        return [];
    }
  };

  const updateField = (field: keyof TankTruckDispatchData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // 当货物类型改变时，重置砂浆类型
      if (field === 'materialType') {
        newData.mortarType = '';
      }

      return newData;
    });
  };

  const handleSave = () => {
    console.log('保存发车单:', formData);
    onOpenChange(false);
  };

  const handleDispatch = () => {
    console.log('调出车辆:', formData);
    onConfirm(formData);
    onOpenChange(false);
  };

  const handleConfirm = () => {
    onConfirm(formData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-7xl max-h-[95vh] overflow-hidden p-0 [&>button]:hidden'>
        <DialogTitle className='sr-only'>罐车发车单</DialogTitle>
        <div className='p-0 px-4 py-0.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg'>
          <div className='flex items-center justify-between text-base font-semibold'>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-white rounded-full'></div>
              <Truck className='w-4 h-4' />
              <span>罐车发车单</span>
            </div>
          </div>
        </div>

        <div className='max-h-[calc(95vh-80px)] overflow-y-auto px-4 space-y-2 bg-gradient-to-b from-transparent to-slate-50/50'>
          {/* 第一行：发货单编号、任务编号、合同编号 */}
          <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-2'>
            <div className='grid grid-cols-3 gap-3'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap flex items-center gap-1'>
                  <AlertCircle className='w-3 h-3 text-red-500' />
                  发货单编号：
                </Label>
                <Input
                  value={formData.dispatchNumber}
                  onChange={e => updateField('dispatchNumber', e.target.value)}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                  placeholder='请输入发货单编号'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>任务编号：</Label>
                <Input
                  value={formData.taskNumber}
                  className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700'
                  readOnly
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>合同编号：</Label>
                <Input
                  value={formData.contractNumber}
                  className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700'
                  readOnly
                />
              </div>
            </div>
          </div>

          {/* 第二行：货物类型、砂浆类型、工程名称 */}
          <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-2'>
            <div className='grid grid-cols-3 gap-3'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap flex items-center gap-1'>
                  <AlertCircle className='w-3 h-3 text-red-500' />
                  货物类型：
                </Label>
                <Select
                  value={formData.materialType}
                  onValueChange={v => updateField('materialType', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='请选择货物类型' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='砼'>砼</SelectItem>
                    <SelectItem value='砂浆'>砂浆</SelectItem>
                    <SelectItem value='水票'>水票</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>砂浆类型：</Label>
                <Select
                  value={formData.mortarType}
                  onValueChange={v => updateField('mortarType', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='请选择砂浆类型' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='普通砂浆'>普通砂浆</SelectItem>
                    <SelectItem value='特种砂浆'>特种砂浆</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap flex items-center gap-1'>
                  <AlertCircle className='w-3 h-3 text-red-500' />
                  工程名称：
                </Label>
                <Input
                  value={formData.projectName}
                  onChange={e => updateField('projectName', e.target.value)}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                  placeholder='请输入工程名称'
                />
              </div>
            </div>
          </div>

          {/* 第三行：站号、主机操作、建设单位 */}
          <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-2'>
            <div className='grid grid-cols-3 gap-3'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap flex items-center gap-1'>
                  <AlertCircle className='w-3 h-3 text-red-500' />
                  站号：
                </Label>
                <Select
                  value={formData.stationNumber}
                  onValueChange={v => updateField('stationNumber', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='请选择站号' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='站点1'>站点1</SelectItem>
                    <SelectItem value='站点2'>站点2</SelectItem>
                    <SelectItem value='站点3'>站点3</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>主机操作：</Label>
                <Select
                  value={formData.mainOperator}
                  onValueChange={v => updateField('mainOperator', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='请选择主机操作' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='操作员1'>操作员1</SelectItem>
                    <SelectItem value='操作员2'>操作员2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap flex items-center gap-1'>
                  <AlertCircle className='w-3 h-3 text-red-500' />
                  建设单位：
                </Label>
                <Input
                  value={formData.constructionUnit}
                  onChange={e => updateField('constructionUnit', e.target.value)}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                  placeholder='请输入建设单位'
                />
              </div>
            </div>
          </div>

          {/* 第四行：车号、司机、施工部位 */}
          <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-2'>
            <div className='grid grid-cols-3 gap-3'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap flex items-center gap-1'>
                  <AlertCircle className='w-3 h-3 text-red-500' />
                  车号：
                </Label>
                <Select
                  value={formData.vehicleNumber}
                  onValueChange={v => updateField('vehicleNumber', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='请选择车号' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='10'>10</SelectItem>
                    <SelectItem value='11'>11</SelectItem>
                    <SelectItem value='12'>12</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>司机：</Label>
                <Select
                  value={formData.driverName}
                  onValueChange={v => updateField('driverName', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='请选择司机' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='未志国'>未志国</SelectItem>
                    <SelectItem value='张三'>张三</SelectItem>
                    <SelectItem value='李四'>李四</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap flex items-center gap-1'>
                  <AlertCircle className='w-3 h-3 text-red-500' />
                  施工部位：
                </Label>
                <Input
                  value={formData.constructionPart}
                  onChange={e => updateField('constructionPart', e.target.value)}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                  placeholder='请输入施工部位'
                />
              </div>
            </div>
          </div>

          {/* 第五行：原检员、出机温度、强度等级 */}
          <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-2'>
            <div className='grid grid-cols-3 gap-3'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>原检员：</Label>
                <Select
                  value={formData.qualityInspector}
                  onValueChange={v => updateField('qualityInspector', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='请选择原检员' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='质检员1'>质检员1</SelectItem>
                    <SelectItem value='质检员2'>质检员2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>出机温度：</Label>
                <Input
                  value={formData.outputTemperature}
                  onChange={e => updateField('outputTemperature', e.target.value)}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                  placeholder='请输入出机温度'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap flex items-center gap-1'>
                  <AlertCircle className='w-3 h-3 text-red-500' />
                  强度等级：
                </Label>
                <Input
                  value={formData.strengthGrade}
                  onChange={e => updateField('strengthGrade', e.target.value)}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                  placeholder='请输入强度等级'
                />
              </div>
            </div>
          </div>

          {/* 第六行：浇筑方式、现场调度、调度员 */}
          <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-2'>
            <div className='grid grid-cols-3 gap-3'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs font-medium text-slate-600 whitespace-nowrap flex items-center gap-1'>
                  <AlertCircle className='w-3 h-3 text-red-500' />
                  浇筑方式：
                </Label>
                <Select
                  value={formData.pouringMethod}
                  onValueChange={v => updateField('pouringMethod', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='请选择浇筑方式' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='自卸'>自卸</SelectItem>
                    <SelectItem value='泵送'>泵送</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>现场调度：</Label>
                <Select
                  value={formData.siteDispatcher}
                  onValueChange={v => updateField('siteDispatcher', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='请选择现场调度' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='调度员1'>调度员1</SelectItem>
                    <SelectItem value='调度员2'>调度员2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>调度员：</Label>
                <Input
                  value={formData.dispatcher}
                  className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700'
                  readOnly
                />
              </div>
            </div>
          </div>

          {/* 第七行：方量设定和打印设置 */}
          <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-2'>
            <div className='grid grid-cols-6 gap-3'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>方量设定：</Label>
                <div className='flex items-center gap-1'>
                  <Checkbox
                    checked={formData.maxSetting}
                    onCheckedChange={checked => updateField('maxSetting', checked)}
                    className='h-4 w-4'
                  />
                  <span className='text-xs text-gray-600'>最大设定</span>
                </div>
              </div>
              <div className='flex items-center gap-1'>
                <div className='flex items-center gap-1'>
                  <Checkbox
                    checked={formData.noOverload}
                    onCheckedChange={checked => updateField('noOverload', checked)}
                    className='h-4 w-4'
                  />
                  <span className='text-xs text-gray-600'>不超载</span>
                </div>
              </div>
              <div className='flex items-center gap-1'>
                <div className='flex items-center gap-1'>
                  <Checkbox
                    checked={formData.adjustByWeight}
                    onCheckedChange={checked => updateField('adjustByWeight', checked)}
                    className='h-4 w-4'
                  />
                  <span className='text-xs text-gray-600'>根据皮重实时调整</span>
                </div>
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>打印方量：</Label>
                <Input
                  type='number'
                  value={formData.printVolume}
                  onChange={e => updateField('printVolume', Number(e.target.value))}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                  placeholder='6.57'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>打印重量：</Label>
                <Input
                  type='number'
                  value={formData.printWeight}
                  onChange={e => updateField('printWeight', Number(e.target.value))}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                  placeholder='130.04'
                />
              </div>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>发车方式：</Label>
                <Select
                  value={formData.dispatchMethod}
                  onValueChange={v => updateField('dispatchMethod', v)}
                >
                  <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100'>
                    <SelectValue placeholder='生成发货单' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='生成发货单'>生成发货单</SelectItem>
                    <SelectItem value='直接发车'>直接发车</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* 主要数据表格区域 */}
          <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-2'>
            <div className='grid grid-cols-2 gap-4'>
              {/* 左侧：车辆信息表格 */}
              <div>
                <div className='text-xs font-medium text-slate-700 mb-2 flex items-center gap-1'>
                  <Truck className='w-3 h-3' />
                  车辆信息
                </div>
                <div className='border border-slate-200 rounded'>
                  <table className='w-full text-xs'>
                    <thead className='bg-slate-50'>
                      <tr>
                        <th className='border-r border-slate-200 p-1 text-left'>车号</th>
                        <th className='border-r border-slate-200 p-1 text-left'>司机</th>
                        <th className='border-r border-slate-200 p-1 text-left'>方量</th>
                        <th className='border-r border-slate-200 p-1 text-left'>超量</th>
                        <th className='p-1 text-left'>超重</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className='border-t border-slate-200'>
                        <td className='border-r border-slate-200 p-1'>{formData.vehicleNumber || '10'}</td>
                        <td className='border-r border-slate-200 p-1'>{formData.driverName || '未志国'}</td>
                        <td className='border-r border-slate-200 p-1'>6.57</td>
                        <td className='border-r border-slate-200 p-1'>-</td>
                        <td className='p-1'>-</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* 右侧：其他设置 */}
              <div className='space-y-2'>
                <div className='grid grid-cols-2 gap-2'>
                  <div>
                    <Label className='text-xs text-gray-600'>生产提示：</Label>
                    <Select
                      value={formData.productionTip}
                      onValueChange={v => updateField('productionTip', v)}
                    >
                      <SelectTrigger className='h-7 text-xs border-slate-200'>
                        <SelectValue placeholder='请选择' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='正常'>正常</SelectItem>
                        <SelectItem value='紧急'>紧急</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className='text-xs text-gray-600'>司机提示：</Label>
                    <Select
                      value={formData.driverTip}
                      onValueChange={v => updateField('driverTip', v)}
                    >
                      <SelectTrigger className='h-7 text-xs border-slate-200'>
                        <SelectValue placeholder='请选择' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='正常'>正常</SelectItem>
                        <SelectItem value='注意'>注意</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Input
                    value={formData.pumpTruckNumber}
                    onChange={e => updateField('pumpTruckNumber', e.target.value)}
                    className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors'
                    placeholder='请输入泵车号'
                  />
                </div>

                <div>
                  <Label className='text-xs text-gray-600'>打印提示：</Label>
                  <Select
                    value={formData.printTip}
                    onValueChange={v => updateField('printTip', v)}
                  >
                    <SelectTrigger className='h-7 text-xs border-slate-200'>
                      <SelectValue placeholder='请选择' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='正常'>正常</SelectItem>
                      <SelectItem value='特殊'>特殊</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 复选框选项 */}
                <div className='grid grid-cols-2 gap-2 mt-2'>
                  <div className='flex items-center gap-1'>
                    <Checkbox
                      checked={formData.autoDispatch}
                      onCheckedChange={checked => updateField('autoDispatch', checked)}
                      className='h-4 w-4'
                    />
                    <span className='text-xs text-gray-600'>自动调度</span>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Checkbox
                      checked={formData.backSandMortar}
                      onCheckedChange={checked => updateField('backSandMortar', checked)}
                      className='h-4 w-4'
                    />
                    <span className='text-xs text-gray-600'>背砂浆</span>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Checkbox
                      checked={formData.washingWater}
                      onCheckedChange={checked => updateField('washingWater', checked)}
                      className='h-4 w-4'
                    />
                    <span className='text-xs text-gray-600'>接洗水</span>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Checkbox
                      checked={formData.withPump}
                      onCheckedChange={checked => updateField('withPump', checked)}
                      className='h-4 w-4'
                    />
                    <span className='text-xs text-gray-600'>带泵车</span>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Checkbox
                      checked={formData.noMixingStation}
                      onCheckedChange={checked => updateField('noMixingStation', checked)}
                      className='h-4 w-4'
                    />
                    <span className='text-xs text-gray-600'>不发往搅拌站</span>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Checkbox
                      checked={formData.allowHouseRepair}
                      onCheckedChange={checked => updateField('allowHouseRepair', checked)}
                      className='h-4 w-4'
                    />
                    <span className='text-xs text-gray-600'>允许房修方量</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>

        {/* 底部按钮区域 */}
        <div className='flex items-center justify-between px-4 py-2 bg-slate-50 border-t border-slate-200'>
          <div className='flex items-center gap-2'>
            <div className='flex items-center gap-1'>
              <Checkbox
                checked={formData.printTicket}
                onCheckedChange={checked => updateField('printTicket', checked)}
                className='h-4 w-4'
              />
              <span className='text-xs text-gray-600'>打票</span>
            </div>
            <Input
              value={formData.ticketNote}
              onChange={e => updateField('ticketNote', e.target.value)}
              className='h-7 text-xs border-slate-200 w-32'
              placeholder='备注信息'
            />
          </div>

          <div className='flex items-center gap-2'>
            <Button
              variant='outline'
              size='sm'
              className='h-8 px-4 text-xs'
              onClick={() => onOpenChange(false)}
            >
              取消
            </Button>
            <Button
              variant='default'
              size='sm'
              className='h-8 px-4 text-xs bg-blue-600 hover:bg-blue-700'
              onClick={handleSave}
            >
              <Save className='w-3 h-3 mr-1' />
              保存
            </Button>
            <Button
              variant='default'
              size='sm'
              className='h-8 px-4 text-xs bg-green-600 hover:bg-green-700'
              onClick={handleDispatch}
            >
              <CheckCircle2 className='w-3 h-3 mr-1' />
              调出
            </Button>
          </div>
        </div>
    </Dialog>
  );
};
